# your_app/utils.py

import logging
from typing import Optional, Dict, Any
from channels.db import database_sync_to_async

logger = logging.getLogger(__name__)

class StepInfo:
    """步骤信息类，用于统一处理用户模板步骤和系统模板步骤"""
    def __init__(self, order: int, step_type: str, name: str = "", duration: int = 300, configuration: Dict[Any, Any] = None):
        self.order = order
        self.step_type = step_type
        self.name = name
        self.duration = duration
        self.configuration = configuration or {}

def advance_to_next_step_sync(room) -> Optional[StepInfo]:
    """
    将房间推进到下一个环节，并返回下一个环节的对象。
    如果没有更多环节，则返回 None。

    支持用户模板和系统模板两种情况：
    - 用户模板：从 room.event_template.steps 获取步骤
    - 系统模板：从系统模板的 template_config 获取步骤
    """
    from .models import Room

    if room.status == Room.STATUS_CLOSED:
        return None

    next_step = None

    if room.event_template:
        # 用户模板：从EventStep模型获取步骤
        next_steps = room.event_template.steps.filter(
            order__gt=room.current_step_order
        ).order_by('order')

        event_step = next_steps.first()
        if event_step:
            next_step = StepInfo(
                order=event_step.order,
                step_type=event_step.step_type,
                name=event_step.name or event_step.get_step_type_display(),
                duration=event_step.duration,
                configuration=event_step.configuration
            )
    else:
        # 系统模板：从房间的系统模板配置获取步骤
        next_step = _get_next_system_template_step(room)

    if next_step:
        # 如果有下一个环节，更新房间状态
        room.current_step_order = next_step.order
        room.status = Room.STATUS_IN_PROGRESS
        logger.info(f"房间 {room.room_code} 已推进到环节 {next_step.order}: {next_step.step_type}")
    else:
        # 如果没有更多环节，回到READY状态等待房主添加新环节或开始下一轮
        # 这是持久化房间的核心：每个环节结束后都回到大厅，等待房主决定下一步
        room.status = Room.STATUS_READY
        logger.info(f"房间 {room.room_code} 已完成当前环节，回到READY状态（大厅）等待房主添加新环节。")

    # 保存对房间状态的更改
    room.save()
    return next_step

# 异步版本，用于WebSocket消费者
advance_to_next_step = database_sync_to_async(advance_to_next_step_sync)

def _get_next_system_template_step(room) -> Optional[StepInfo]:
    """
    从系统模板获取下一个步骤

    Args:
        room: 房间对象

    Returns:
        StepInfo: 下一个步骤信息，如果没有则返回None
    """
    # 从房间的system_template字段获取系统模板
    if not room.system_template:
        logger.warning(f"房间 {room.room_code} 没有关联的系统模板，无法获取步骤")
        return None

    try:
        steps = room.system_template.get_steps()

        # 找到下一个步骤
        for step_data in steps:
            if step_data.get('order', 0) > room.current_step_order:
                return StepInfo(
                    order=step_data.get('order', 0),
                    step_type=step_data.get('step_type', 'FREE_CHAT'),
                    name=step_data.get('name', ''),
                    duration=step_data.get('duration', 300),
                    configuration=step_data.get('configuration', {})
                )

        return None

    except Exception as e:
        logger.error(f"获取系统模板步骤时发生错误: {e}")
        return None

def advance_to_next_step_sync(room) -> Optional[StepInfo]:
    """
    同步版本的advance_to_next_step，用于测试
    """
    from .models import Room

    if room.status == Room.STATUS_CLOSED:
        return None

    next_step = None

    if room.event_template:
        # 用户模板：从EventStep模型获取步骤
        next_steps = room.event_template.steps.filter(
            order__gt=room.current_step_order
        ).order_by('order')

        event_step = next_steps.first()
        if event_step:
            next_step = StepInfo(
                order=event_step.order,
                step_type=event_step.step_type,
                name=event_step.name or event_step.get_step_type_display(),
                duration=event_step.duration,
                configuration=event_step.configuration
            )
    else:
        # 系统模板：从房间的系统模板配置获取步骤
        next_step = _get_next_system_template_step(room)

    if next_step:
        # 如果有下一个环节，更新房间状态
        room.current_step_order = next_step.order
        room.status = Room.STATUS_IN_PROGRESS
        logger.info(f"房间 {room.room_code} 已推进到环节 {next_step.order}: {next_step.step_type}")
    else:
        # 如果没有更多环节，返回到READY状态等待房主添加新环节
        # 这是持久化房间的核心：不自动结束，而是等待新的环节
        room.status = Room.STATUS_READY
        logger.info(f"房间 {room.room_code} 已完成当前模板的所有环节，返回大厅等待房主添加新环节。")

    # 保存对房间状态的更改
    room.save()
    return next_step

@database_sync_to_async
def save_room(room):
    """
    异步地保存房间对象到数据库。
    """
    try:
        room.save()
        logger.debug(f"成功保存房间 {room.room_code} 的状态。")
    except Exception as e:
        logger.error(f"保存房间 {room.room_code} 时出错: {e}")

# get_room_with_template 方法已被 RoomConsumer 内的 get_room_with_host 替代，
# 因此可以从这个文件中移除，以保持代码整洁。

