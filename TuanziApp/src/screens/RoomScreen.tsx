import React, { useState, useEffect, useRef, useCallback } from 'react';
import { View, Text, StyleSheet, SafeAreaView, Alert, ActivityIndicator, TouchableOpacity } from 'react-native';
import { useRoute, RouteProp, useNavigation } from '@react-navigation/native';
import { useAuth } from '../auth/AuthContext';
import { WEBSOCKET_URL_BASE } from '../api/client';
import { RootStackParamList, EventStep, Message, PictionaryState } from '../types';

import { PictionaryView } from '../components/steps/PictionaryView';
import { ChatView } from '../components/steps/ChatView';
import { LobbyView } from '../components/steps/LobbyView';
import { PathData } from '../components/PictionaryCanvas';
import { ActionPanel } from '../components/ActionPanel';
import { AddStepModal } from '../components/AddStepModal';
import { addStepToRoom } from '../api/eventApi';

type RoomScreenRouteProp = RouteProp<RootStackParamList, 'Room'>;

export const RoomScreen = () => {
    const route = useRoute<RoomScreenRouteProp>();
    const navigation = useNavigation();
    const initialRoom = route.params.room;
    const { user, token } = useAuth();

    const [currentStep, setCurrentStep] = useState<EventStep | null>(null);
    const [_stepPayload, setStepPayload] = useState<any>(null);
    const [roomStatus, setRoomStatus] = useState(initialRoom.status || 'READY');
    const [isWsConnected, setIsWsConnected] = useState(false);
    const [reconnectAttempts, setReconnectAttempts] = useState(0);
    const maxReconnectAttempts = 5;
    const [hasShownOfflineAlert, setHasShownOfflineAlert] = useState(false);
    const [wasConnectedBefore, setWasConnectedBefore] = useState(false);
    const lastAlertTime = useRef<number>(0);
    
    const [isActionPanelVisible, setIsActionPanelVisible] = useState(false);
    const [isAddStepModalVisible, setIsAddStepModalVisible] = useState(false);
    
    const [messages, setMessages] = useState<Message[]>([]);
    const [paths, setPaths] = useState<PathData[]>([]);
    const [pictionaryState, setPictionaryState] = useState<PictionaryState | null>(null);
    const localPathIds = useRef<Set<string>>(new Set());
    const drawingThrottle = useRef<{
        lastSentTime: number;
        pendingPath: PathData | null;
        timeoutId: number | null;
    }>({
        lastSentTime: 0,
        pendingPath: null,
        timeoutId: null
    });
    
    const ws = useRef<WebSocket | null>(null);

    const connectWebSocket = useCallback(() => {
        if (!initialRoom || !token) return;

        const wsUrl = `${WEBSOCKET_URL_BASE}/ws/room/${initialRoom.room_code}/?token=${token}`;
        ws.current = new WebSocket(wsUrl);

        ws.current.onopen = () => {
            console.log('WebSocket connected');
            setIsWsConnected(true);
            setReconnectAttempts(0);
            setWasConnectedBefore(true);

            // 如果之前显示过离线提示，现在连接成功了，重置状态
            if (hasShownOfflineAlert) {
                setHasShownOfflineAlert(false);
                console.log('网络已恢复，重置离线提示状态');
            }
        };

        ws.current.onclose = (event) => {
            console.log('WebSocket disconnected:', event.code, event.reason);
            setIsWsConnected(false);

            if (event.code !== 1000 && reconnectAttempts < maxReconnectAttempts) {
                const timeout = Math.min(3000 * Math.pow(2, reconnectAttempts), 30000); // 增加基础延迟到3秒
                setTimeout(() => {
                    setReconnectAttempts(prev => prev + 1);
                    connectWebSocket();
                }, timeout);
            } else if (reconnectAttempts >= maxReconnectAttempts) {
                // 优化弹窗逻辑：降低弹窗频率，避免重复弹窗
                const now = Date.now();
                const minAlertInterval = 60000; // 最少1分钟间隔

                if (!hasShownOfflineAlert && wasConnectedBefore && (now - lastAlertTime.current) > minAlertInterval) {
                    setHasShownOfflineAlert(true);
                    lastAlertTime.current = now;

                    Alert.alert(
                        '网络连接中断',
                        '无法连接到服务器，请检查网络连接。网络恢复后将自动重连。',
                        [{
                            text: '确定',
                            onPress: () => {
                                // 用户确认后，允许下次重连时重新显示提示
                                setTimeout(() => {
                                    setHasShownOfflineAlert(false);
                                }, 30000); // 30秒后允许再次显示
                            }
                        }]
                    );
                }
            }
        };

        ws.current.onerror = (e: any) => {
            console.error('WebSocket error:', e.message);
        };

        ws.current.onmessage = (e: any) => {
            try {
                const data = JSON.parse(e.data);

                switch (data.type) {
                    case 'step_started':
                        setCurrentStep(data.payload.step_info);
                        setStepPayload(data.payload);
                        setRoomStatus(data.payload.room_status);
                        setMessages([]);
                        setPaths([]);
                        localPathIds.current.clear();
                        if (data.payload.step_info.step_type === 'GAME_PICTIONARY') {
                            setPictionaryState(data.payload);
                        }
                        break;
                    case 'round_over':
                        setCurrentStep(null);
                        setStepPayload(null);
                        setRoomStatus('WAITING');
                        setPictionaryState(null);
                        setPaths([]);
                        localPathIds.current.clear();

                        const alertTitle = data.payload.timeout ? "时间到!" :
                                         data.payload.winner ? "猜对了!" : "游戏结束!";
                        const alertMessage = data.payload.timeout ? `时间到! 正确答案是: ${data.payload.word}` :
                                           data.payload.winner ? `${data.payload.winner} 猜中了答案: ${data.payload.word}` :
                                           `正确答案是: ${data.payload.word}`;

                        if (user?.username === initialRoom.host) {
                            Alert.alert(
                                alertTitle,
                                alertMessage + "\n\n是否再开始一轮？",
                                [
                                    { text: "结束", style: "cancel" },
                                    {
                                        text: "再来一轮",
                                        onPress: () => sendWebSocketMessage('restart_game', { game_type: 'PICTIONARY' })
                                    }
                                ]
                            );
                        } else {
                            Alert.alert(alertTitle, alertMessage);
                        }
                        break;
                    case 'event_finished':
                        Alert.alert("活动结束", data.payload.message);
                        setRoomStatus('FINISHED');
                        setCurrentStep(null);
                        break;
                    case 'game_ended':
                        // 房间被房主强制结束
                        Alert.alert(
                            "房间已结束",
                            data.payload.message,
                            [
                                {
                                    text: "确定",
                                    onPress: () => {
                                        // 断开WebSocket连接
                                        if (ws.current) {
                                            ws.current.close(1000);
                                        }
                                        // 导航回主页面
                                        navigation.reset({
                                            index: 0,
                                            routes: [{ name: 'Home' }],
                                        });
                                    }
                                }
                            ],
                            { cancelable: false }
                        );
                        break;
                    case 'new_round':
                        // 新一局开始
                        setPictionaryState({
                            drawer: data.payload.drawer,
                            word: data.payload.word,
                            round_duration: data.payload.round_duration,
                            current_round: data.payload.current_round,
                            total_rounds: data.payload.total_rounds,
                        });
                        // 清空绘图路径
                        setPaths([]);
                        break;
                    case 'return_to_lobby':
                        // 回到大厅
                        // 如果是所有环节完成，显示不同的提示
                        if (data.payload.reason === 'all_steps_completed') {
                            Alert.alert(
                                "环节完成",
                                data.payload.message,
                                [{ text: "确定" }]
                            );
                        } else {
                            Alert.alert(
                                "回到大厅",
                                data.payload.message,
                                [{ text: "确定" }]
                            );
                        }
                        setRoomStatus('READY');
                        setCurrentStep(null);
                        setStepPayload(null);
                        setPictionaryState(null);
                        setPaths([]);
                        localPathIds.current.clear();
                        break;
                    case 'error':
                        Alert.alert("操作失败", data.payload.message);
                        break;
                    case 'chat_message':
                        setMessages(prev => [data.payload, ...prev]);
                        break;
                    case 'drawing_data':
                        const incomingPath = data.payload.path_data;
                        if (!localPathIds.current.has(incomingPath.id)) {
                            setPaths(prev => [...prev, incomingPath]);
                        }
                        break;
                    case 'step_timeout':
                        setCurrentStep(null);
                        setStepPayload(null);
                        setRoomStatus(data.payload.room_status);
                        setPictionaryState(null);
                        setPaths([]);
                        localPathIds.current.clear();
                        Alert.alert("时间到!", "环节时间已结束");
                        break;
                    default:
                        console.warn('Unknown message type:', data.type);
                }
            } catch (error) {
                console.error('Error parsing WebSocket message:', error);
            }
        };
    }, [initialRoom, token, reconnectAttempts, maxReconnectAttempts]);

    useEffect(() => {
        connectWebSocket();
        return () => {
            if (ws.current) {
                ws.current.close(1000, 'Component unmounting');
            }
            if (drawingThrottle.current.timeoutId) {
                clearTimeout(drawingThrottle.current.timeoutId);
            }
        };
    }, [connectWebSocket]);

    const sendWebSocketMessage = (action: string, payload: object = {}) => {
        if (ws.current?.readyState === WebSocket.OPEN) {
            ws.current.send(JSON.stringify({ action, payload }));
        }
    };

    const handleNextStep = () => {
        sendWebSocketMessage('next_step');
    };

    const handleDraw = (pathData: PathData) => {
        localPathIds.current.add(pathData.id);
        setPaths(prev => [...prev, pathData]);

        const now = Date.now();
        const THROTTLE_MS = 50;

        if (now - drawingThrottle.current.lastSentTime >= THROTTLE_MS) {
            drawingThrottle.current.lastSentTime = now;
            sendWebSocketMessage('send_drawing', { path_data: pathData });
        } else {
            drawingThrottle.current.pendingPath = pathData;

            if (drawingThrottle.current.timeoutId) {
                clearTimeout(drawingThrottle.current.timeoutId);
            }

            const remainingTime = THROTTLE_MS - (now - drawingThrottle.current.lastSentTime);
            drawingThrottle.current.timeoutId = setTimeout(() => {
                if (drawingThrottle.current.pendingPath) {
                    drawingThrottle.current.lastSentTime = Date.now();
                    sendWebSocketMessage('send_drawing', {
                        path_data: drawingThrottle.current.pendingPath
                    });
                    drawingThrottle.current.pendingPath = null;
                }
                drawingThrottle.current.timeoutId = null;
            }, remainingTime);
        }
    };

    const handleSendMessage = (message: string) => {
        sendWebSocketMessage('send_message', { message });
    };

    const handleAddStep = async (stepData: {
        step_type: string;
        name?: string;
        duration?: number;
        configuration?: Record<string, any>;
    }) => {
        try {
            const result = await addStepToRoom(initialRoom.room_code, stepData);
            Alert.alert('成功', result.message);
            // 可以在这里刷新房间状态或通过WebSocket通知其他用户
        } catch (error) {
            console.error('添加环节失败:', error);
            Alert.alert('错误', error instanceof Error ? error.message : '添加环节失败');
            throw error; // 重新抛出错误，让Modal处理
        }
    };

    const handleReturnToLobby = () => {
        sendWebSocketMessage('return_to_lobby', {});
    };

    const handleEndActivity = () => {
        Alert.alert(
            '确认结束',
            '确定要结束整个活动吗？这将关闭房间。',
            [
                { text: '取消', style: 'cancel' },
                {
                    text: '确定',
                    style: 'destructive',
                    onPress: () => {
                        sendWebSocketMessage('end_game', {});
                    }
                }
            ]
        );
    };

    const handleForceNextStep = () => {
        // 强制进入下一环节
        sendWebSocketMessage('next_step', {});
    };

    const renderCurrentStep = () => {
        const isHost = user?.username === initialRoom.host;

        if (roomStatus === 'FINISHED') {
            return (
                <LobbyView
                    isFinished={true}
                    messages={messages}
                    onSendMessage={handleSendMessage}
                />
            );
        }

        if (!currentStep || roomStatus === 'WAITING' || roomStatus === 'READY') {
            return (
                <LobbyView
                    isHost={isHost}
                    onNextStep={handleNextStep}
                    isConnected={isWsConnected}
                    messages={messages}
                    onSendMessage={handleSendMessage}
                    onAddStep={() => setIsAddStepModalVisible(true)}
                    // TODO: 添加活动历史和参与者数据
                    activityHistory={[]}
                    participants={[]}
                />
            );
        }

        switch (currentStep.step_type) {
            case 'GAME_PICTIONARY':
                if (!pictionaryState) {
                    return <View style={styles.container}><ActivityIndicator size="large" /></View>;
                }
                return (
                    <PictionaryView
                        isDrawer={user?.username === pictionaryState.drawer}
                        pictionaryState={pictionaryState}
                        paths={paths}
                        messages={messages}
                        onDraw={handleDraw}
                        onSendMessage={handleSendMessage}
                        onSendGuess={handleSendMessage} // 暂时使用相同的处理器，后续可以分离
                    />
                );
            case 'FREE_CHAT':
                return (
                    <ChatView
                        messages={messages}
                        onSendMessage={handleSendMessage}
                        roomHost={initialRoom.host}
                    />
                );
            default:
                return (
                    <LobbyView
                        isHost={isHost}
                        onNextStep={handleNextStep}
                        isConnected={isWsConnected}
                        messages={messages}
                        onSendMessage={handleSendMessage}
                        onAddStep={() => setIsAddStepModalVisible(true)}
                        activityHistory={[]}
                        participants={[]}
                    />
                );
        }
    };

    return (
        <SafeAreaView style={styles.safeArea}>
            <View style={styles.container}>
                {/* --- 核心更改：新的标题栏 --- */}
                <View style={styles.header}>
                    <View style={styles.headerTitleContainer}>
                        <Text style={styles.title}>房间: {initialRoom.room_code}</Text>
                    </View>
                    <TouchableOpacity
                        style={styles.headerButton}
                        onPress={() => setIsActionPanelVisible(true)}
                    >
                        <Text style={styles.headerButtonIcon}>⚙️</Text>
                    </TouchableOpacity>
                </View>
                
                {renderCurrentStep()}

                {/* 全局操作面板 */}
                <ActionPanel
                    isVisible={isActionPanelVisible}
                    onClose={() => setIsActionPanelVisible(false)}
                    roomHost={initialRoom.host}
                    onNextStep={handleNextStep}
                    onAddStep={() => setIsAddStepModalVisible(true)}
                    onEndActivity={handleEndActivity}
                    onForceNextStep={handleForceNextStep}
                    onReturnToLobby={handleReturnToLobby}
                    roomStatus={roomStatus}
                />

                {/* 添加环节模态框 */}
                <AddStepModal
                    isVisible={isAddStepModalVisible}
                    onClose={() => setIsAddStepModalVisible(false)}
                    onAddStep={handleAddStep}
                    roomCode={initialRoom.room_code}
                />
            </View>
        </SafeAreaView>
    );
};

const styles = StyleSheet.create({
    safeArea: { flex: 1, backgroundColor: '#fff' },
    container: { flex: 1 },
    // --- 新增样式 ---
    header: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'center',
        paddingHorizontal: 20,
        paddingVertical: 10,
        borderBottomWidth: 1,
        borderBottomColor: '#eee',
    },
    headerTitleContainer: {
        flex: 1, // 占据剩余空间，确保标题居中
        alignItems: 'center',
    },
    title: { 
        fontSize: 22, 
        fontWeight: 'bold',
        // 调整左边距以更好地居中
        marginLeft: 40, 
    },
    headerButton: {
        width: 40,
        height: 40,
        justifyContent: 'center',
        alignItems: 'center',
    },
    headerButtonIcon: {
        fontSize: 24,
    },
});
