import React, { useState } from 'react';
import { View, Text, StyleSheet, TextInput, Alert, ScrollView, TouchableOpacity } from 'react-native';
import { useAuth } from '../auth/AuthContext';
import { useSubscription } from '../contexts/SubscriptionContext';
import { API_URL } from '../api/client';
import { useNavigation } from '@react-navigation/native';
import { StackNavigationProp } from '@react-navigation/stack';
import { RootStackParamList } from '../types';
import { Card, Button, Badge, Typography, Screen } from '../components';
import { theme } from '../styles/theme';

type HomeScreenNavigationProp = StackNavigationProp<RootStackParamList, 'Home'>;

// 导航卡片数据
interface NavigationCard {
  id: string;
  title: string;
  description: string;
  icon: string;
  color: string;
  route: keyof RootStackParamList;
  params?: any;
  requiresSubscription?: 'Pro' | 'Max';
}

export const HomeScreen = () => {
  const { user, logout, token } = useAuth();
  const { subscriptionInfo } = useSubscription();
  const [roomCodeInput, setRoomCodeInput] = useState('');
  const navigation = useNavigation<HomeScreenNavigationProp>();

  const handleJoinRoom = async () => {
    if (!roomCodeInput.trim()) {
      Alert.alert('提示', '请输入房间代码');
      return;
    }

    try {
      const response = await fetch(`${API_URL}/api/rooms/join/`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json', 'Authorization': `Bearer ${token}` },
        body: JSON.stringify({ room_code: roomCodeInput.trim() }),
      });
      const data = await response.json();
      if (response.ok) {
        navigation.navigate('Room', { room: data.room });
      } else {
        Alert.alert('加入失败', data.error || '无法加入房间。');
      }
    } catch (error) {
      console.error(error);
      Alert.alert('错误', '加入房间时发生错误。');
    }
  };

  // 导航卡片配置
  const navigationCards: NavigationCard[] = [
    {
      id: 'create-room',
      title: '创建房间',
      description: '开始一个新的游戏房间',
      icon: '🎮',
      color: theme.colors.primary,
      route: 'CreateRoom',
    },
    {
      id: 'event-designer',
      title: '环节设计器',
      description: '设计自定义游戏环节',
      icon: '🎨',
      color: theme.colors.secondary,
      route: 'EventDesigner',
      requiresSubscription: 'Pro',
    },
    {
      id: 'calendar',
      title: '日历预约',
      description: '预约未来的游戏时间',
      icon: '📅',
      color: theme.colors.tertiary,
      route: 'Calendar',
      requiresSubscription: 'Pro',
    },
    {
      id: 'subscription',
      title: '订阅管理',
      description: '管理您的订阅计划',
      icon: '💎',
      color: theme.colors.vibrant.coral,
      route: 'Subscription',
    },
  ];

  const renderNavigationCard = (card: NavigationCard) => {
    const isLocked = card.requiresSubscription &&
      (!subscriptionInfo || subscriptionInfo.current_level === 'Free');

    return (
      <Card
        key={card.id}
        style={StyleSheet.flatten([styles.navigationCard, { borderLeftColor: card.color }])}
        variant="elevated"
        onPress={() => {
          if (isLocked) {
            Alert.alert(
              '需要订阅',
              `此功能需要${card.requiresSubscription}订阅。是否前往订阅页面？`,
              [
                { text: '取消', style: 'cancel' },
                { text: '前往订阅', onPress: () => navigation.navigate('Subscription') }
              ]
            );
          } else {
            navigation.navigate(card.route as any);
          }
        }}
      >
        <View style={styles.cardContent}>
          <View style={styles.cardHeader}>
            <Text style={styles.cardIcon}>{card.icon}</Text>
            <View style={styles.cardTitleContainer}>
              <Text style={styles.cardTitle}>{card.title}</Text>
              {isLocked && (
                <Badge
                  text={card.requiresSubscription!}
                  variant="warning"
                  size="xs"
                />
              )}
            </View>
          </View>
          <Text style={styles.cardDescription}>{card.description}</Text>
        </View>
      </Card>
    );
  };

  return (
    <Screen style={styles.container}>
      {/* 头部欢迎区域 */}
      <View style={styles.header}>
        <View style={styles.welcomeSection}>
          <Text style={styles.greeting}>你好！</Text>
          <Text style={styles.username}>{user?.username}</Text>
        </View>

        {/* 订阅状态 */}
        <Card variant="filled" style={styles.subscriptionCard}>
          <View style={styles.subscriptionContent}>
            <Badge
              text={subscriptionInfo?.current_level || 'Free'}
              variant={subscriptionInfo?.current_level === 'Free' ? 'neutral' : 'primary'}
              size="small"
            />
            <Text style={styles.subscriptionLabel}>当前订阅</Text>
          </View>
        </Card>
      </View>

      {/* 快速加入房间 */}
      <Card style={styles.joinCard} variant="outlined">
        <Text style={styles.joinTitle}>快速加入房间</Text>
        <View style={styles.joinInputContainer}>
          <TextInput
            style={styles.joinInput}
            placeholder="输入6位房间代码"
            value={roomCodeInput}
            onChangeText={setRoomCodeInput}
            autoCapitalize="characters"
            maxLength={6}
            placeholderTextColor={theme.colors.textTertiary}
          />
          <Button
            title="加入"
            onPress={handleJoinRoom}
            size="medium"
            disabled={!roomCodeInput.trim()}
          />
        </View>
      </Card>

      {/* 导航卡片网格 */}
      <View style={styles.navigationGrid}>
        <Text style={styles.sectionTitle}>功能导航</Text>
        {navigationCards.map(renderNavigationCard)}
      </View>

      {/* 登出按钮 */}
      <View style={styles.logoutContainer}>
        <Button
          title="登出"
          onPress={logout}
          variant="outline"
          size="medium"
          fullWidth
        />
      </View>
    </Screen>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: theme.colors.background,
  },

  // 头部区域
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: theme.spacing['2xl'],
  },
  welcomeSection: {
    flex: 1,
  },
  greeting: {
    fontSize: theme.typography.fontSize.lg,
    color: theme.colors.textSecondary,
    fontWeight: theme.typography.fontWeight.medium,
  },
  username: {
    fontSize: theme.typography.fontSize['3xl'],
    color: theme.colors.textPrimary,
    fontWeight: theme.typography.fontWeight.bold,
    marginTop: theme.spacing.xs,
  },

  // 订阅状态卡片
  subscriptionCard: {
    minWidth: 100,
  },
  subscriptionContent: {
    alignItems: 'center',
    gap: theme.spacing.xs,
  },
  subscriptionLabel: {
    fontSize: theme.typography.fontSize.xs,
    color: theme.colors.textSecondary,
    textAlign: 'center',
  },

  // 快速加入区域
  joinCard: {
    marginBottom: theme.spacing['2xl'],
  },
  joinTitle: {
    fontSize: theme.typography.fontSize.lg,
    fontWeight: theme.typography.fontWeight.semibold,
    color: theme.colors.textPrimary,
    marginBottom: theme.spacing.md,
  },
  joinInputContainer: {
    flexDirection: 'row',
    gap: theme.spacing.md,
    alignItems: 'center',
  },
  joinInput: {
    flex: 1,
    height: 44,
    borderWidth: 1,
    borderColor: theme.colors.border,
    borderRadius: theme.borderRadius.lg,
    paddingHorizontal: theme.spacing.md,
    fontSize: theme.typography.fontSize.base,
    color: theme.colors.textPrimary,
    backgroundColor: theme.colors.surface,
  },

  // 导航区域
  navigationGrid: {
    flex: 1,
    marginBottom: theme.spacing['2xl'],
  },
  sectionTitle: {
    fontSize: theme.typography.fontSize.xl,
    fontWeight: theme.typography.fontWeight.bold,
    color: theme.colors.textPrimary,
    marginBottom: theme.spacing.lg,
  },

  // 导航卡片
  navigationCard: {
    marginBottom: theme.spacing.md,
    borderLeftWidth: 4,
  },
  cardContent: {
    gap: theme.spacing.sm,
  },
  cardHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: theme.spacing.md,
  },
  cardIcon: {
    fontSize: theme.typography.fontSize['2xl'],
  },
  cardTitleContainer: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  cardTitle: {
    fontSize: theme.typography.fontSize.lg,
    fontWeight: theme.typography.fontWeight.semibold,
    color: theme.colors.textPrimary,
  },
  cardDescription: {
    fontSize: theme.typography.fontSize.sm,
    color: theme.colors.textSecondary,
    marginLeft: theme.typography.fontSize['2xl'] + theme.spacing.md, // 对齐图标
  },

  // 登出区域
  logoutContainer: {
    paddingTop: theme.spacing.lg,
    borderTopWidth: 1,
    borderTopColor: theme.colors.border,
  },
});
