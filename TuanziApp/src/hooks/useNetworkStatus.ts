import { useEffect, useState, useRef } from 'react';
// 注意：这是一个简化版本，不依赖@react-native-community/netinfo
// 在生产环境中建议安装并使用该库以获得更准确的网络状态检测

interface UseNetworkStatusOptions {
  onNetworkRestore?: () => void;
  onNetworkLost?: () => void;
  checkInterval?: number; // 检查间隔，毫秒
}

interface UseNetworkStatusReturn {
  isConnected: boolean;
  isInternetReachable: boolean;
  connectionType: string;
  wasOffline: boolean;
}

export const useNetworkStatus = ({
  onNetworkRestore,
  onNetworkLost,
  checkInterval = 5000, // 默认5秒检查一次，降低频率
}: UseNetworkStatusOptions = {}): UseNetworkStatusReturn => {
  const [isConnected, setIsConnected] = useState(true);
  const [isInternetReachable, setIsInternetReachable] = useState(true);
  const [connectionType, setConnectionType] = useState('unknown');
  const [wasOffline, setWasOffline] = useState(false);
  
  const lastCheckTime = useRef<number>(0);
  const checkTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  const checkNetworkStatus = async () => {
    const now = Date.now();

    // 限制检查频率
    if (now - lastCheckTime.current < checkInterval) {
      return;
    }

    lastCheckTime.current = now;

    try {
      // 简化版本：通过尝试fetch一个轻量级端点来检测网络连接
      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), 5000); // 5秒超时

      const response = await fetch('https://www.google.com/favicon.ico', {
        method: 'HEAD',
        signal: controller.signal,
        cache: 'no-cache',
      });

      clearTimeout(timeoutId);

      const newIsConnected = response.ok;
      const newIsInternetReachable = response.ok;
      const newConnectionType = 'unknown'; // 简化版本不检测连接类型

      // 检测网络状态变化
      const wasConnected = isConnected && isInternetReachable;
      const isNowConnected = newIsConnected && newIsInternetReachable;

      // 更新状态
      setIsConnected(newIsConnected);
      setIsInternetReachable(newIsInternetReachable);
      setConnectionType(newConnectionType);

      // 网络恢复
      if (!wasConnected && isNowConnected) {
        console.log('网络已恢复');
        setWasOffline(false);
        onNetworkRestore?.();
      }
      // 网络断开
      else if (wasConnected && !isNowConnected) {
        console.log('网络已断开');
        setWasOffline(true);
        onNetworkLost?.();
      }

    } catch (error) {
      console.error('检查网络状态时出错:', error);
      // 网络检查失败，假设网络断开
      const wasConnected = isConnected && isInternetReachable;

      setIsConnected(false);
      setIsInternetReachable(false);

      if (wasConnected) {
        console.log('网络检查失败，假设网络已断开');
        setWasOffline(true);
        onNetworkLost?.();
      }
    }
  };

  useEffect(() => {
    // 初始检查
    checkNetworkStatus();
    
    // 设置定期检查
    const startPeriodicCheck = () => {
      checkTimeoutRef.current = setTimeout(() => {
        checkNetworkStatus().then(() => {
          startPeriodicCheck(); // 递归调用以继续检查
        });
      }, checkInterval);
    };
    
    startPeriodicCheck();
    
    // 简化版本：不使用事件监听，仅依赖定期检查

    return () => {
      if (checkTimeoutRef.current) {
        clearTimeout(checkTimeoutRef.current);
      }
    };
  }, [checkInterval, isConnected, isInternetReachable, connectionType, onNetworkRestore, onNetworkLost]);

  return {
    isConnected,
    isInternetReachable,
    connectionType,
    wasOffline,
  };
};
