import React, { useState } from 'react';
import {
  View,
  Text,
  Button,
  StyleSheet,
  ActivityIndicator,
  ScrollView,
  TouchableOpacity,
  TextInput,
  FlatList
} from 'react-native';
import { Message } from '../../types';

interface LobbyViewProps {
  isHost?: boolean;
  onNextStep?: () => void;
  isFinished?: boolean;
  isConnected?: boolean;
  // 新增属性
  messages?: Message[];
  onSendMessage?: (message: string) => void;
  activityHistory?: ActivityHistoryItem[];
  participants?: Participant[];
  onAddStep?: () => void;
}

interface ActivityHistoryItem {
  id: string;
  type: 'step_completed' | 'game_result' | 'user_joined' | 'user_left';
  title: string;
  description: string;
  timestamp: string;
  data?: any;
}

interface Participant {
  username: string;
  role: 'host' | 'admin' | 'participant';
  isActive: boolean;
  joinedAt: string;
}

export const LobbyView: React.FC<LobbyViewProps> = ({
  isHost,
  onNextStep,
  isFinished,
  isConnected,
  messages = [],
  onSendMessage,
  activityHistory = [],
  participants = [],
  onAddStep
}) => {
  const [currentMessage, setCurrentMessage] = useState('');
  const [activeTab, setActiveTab] = useState<'chat' | 'history' | 'participants'>('chat');

  const handleSendMessage = () => {
    if (currentMessage.trim() && onSendMessage) {
      onSendMessage(currentMessage.trim());
      setCurrentMessage('');
    }
  };

  const renderTabContent = () => {
    switch (activeTab) {
      case 'chat':
        return (
          <View style={styles.chatContainer}>
            <FlatList
              data={messages}
              keyExtractor={(item, index) => `${item.id || index}`}
              renderItem={({ item }) => (
                <View style={styles.messageItem}>
                  <Text style={styles.messageUser}>{item.user}:</Text>
                  <Text style={styles.messageText}>{item.message}</Text>
                </View>
              )}
              style={styles.messagesList}
              inverted
            />
            {onSendMessage && (
              <View style={styles.messageInputContainer}>
                <TextInput
                  style={styles.messageInput}
                  value={currentMessage}
                  onChangeText={setCurrentMessage}
                  placeholder="输入消息..."
                  multiline
                />
                <TouchableOpacity
                  style={styles.sendButton}
                  onPress={handleSendMessage}
                  disabled={!currentMessage.trim()}
                >
                  <Text style={styles.sendButtonText}>发送</Text>
                </TouchableOpacity>
              </View>
            )}
          </View>
        );

      case 'history':
        return (
          <View style={styles.historyContainer}>
            <Text style={styles.sectionTitle}>活动历史</Text>
            <FlatList
              data={activityHistory}
              keyExtractor={(item) => item.id}
              renderItem={({ item }) => (
                <View style={styles.historyItem}>
                  <Text style={styles.historyTitle}>{item.title}</Text>
                  <Text style={styles.historyDescription}>{item.description}</Text>
                  <Text style={styles.historyTime}>{item.timestamp}</Text>
                </View>
              )}
              ListEmptyComponent={
                <Text style={styles.emptyText}>暂无活动历史</Text>
              }
            />
          </View>
        );

      case 'participants':
        return (
          <View style={styles.participantsContainer}>
            <Text style={styles.sectionTitle}>参与者 ({participants.length})</Text>
            <FlatList
              data={participants}
              keyExtractor={(item) => item.username}
              renderItem={({ item }) => (
                <View style={styles.participantItem}>
                  <Text style={styles.participantName}>
                    {item.username}
                    {item.role === 'host' && ' 👑'}
                    {item.role === 'admin' && ' ⭐'}
                  </Text>
                  <Text style={[
                    styles.participantStatus,
                    { color: item.isActive ? '#4CAF50' : '#999' }
                  ]}>
                    {item.isActive ? '在线' : '离线'}
                  </Text>
                </View>
              )}
              ListEmptyComponent={
                <Text style={styles.emptyText}>暂无参与者</Text>
              }
            />
          </View>
        );

      default:
        return null;
    }
  };

  return (
    <View style={styles.container}>
      {/* 状态栏 */}
      <View style={styles.statusBar}>
        {isFinished ? (
          <Text style={styles.statusText}>🎉 所有环节已结束！</Text>
        ) : (
          <>
            <Text style={styles.statusText}>
              {isHost ? '🎮 您是房主，可以添加新环节' : '⏳ 等待房主开始下一环节...'}
            </Text>
            {!isConnected && <ActivityIndicator style={styles.spinner} />}
          </>
        )}
      </View>

      {/* 房主操作区 */}
      {isHost && !isFinished && (
        <View style={styles.hostActions}>
          <TouchableOpacity
            style={[styles.actionButton, styles.primaryButton]}
            onPress={onNextStep}
            disabled={!isConnected}
          >
            <Text style={styles.actionButtonText}>
              {isConnected ? '📋 开始下一环节' : '正在连接...'}
            </Text>
          </TouchableOpacity>

          {onAddStep && (
            <TouchableOpacity
              style={[styles.actionButton, styles.secondaryButton]}
              onPress={onAddStep}
              disabled={!isConnected}
            >
              <Text style={styles.actionButtonText}>➕ 添加新环节</Text>
            </TouchableOpacity>
          )}
        </View>
      )}

      {/* 标签页导航 */}
      <View style={styles.tabContainer}>
        <TouchableOpacity
          style={[styles.tab, activeTab === 'chat' && styles.activeTab]}
          onPress={() => setActiveTab('chat')}
        >
          <Text style={[styles.tabText, activeTab === 'chat' && styles.activeTabText]}>
            💬 聊天
          </Text>
        </TouchableOpacity>
        <TouchableOpacity
          style={[styles.tab, activeTab === 'history' && styles.activeTab]}
          onPress={() => setActiveTab('history')}
        >
          <Text style={[styles.tabText, activeTab === 'history' && styles.activeTabText]}>
            📊 历史
          </Text>
        </TouchableOpacity>
        <TouchableOpacity
          style={[styles.tab, activeTab === 'participants' && styles.activeTab]}
          onPress={() => setActiveTab('participants')}
        >
          <Text style={[styles.tabText, activeTab === 'participants' && styles.activeTabText]}>
            👥 成员
          </Text>
        </TouchableOpacity>
      </View>

      {/* 内容区域 */}
      <View style={styles.contentContainer}>
        {renderTabContent()}
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  statusBar: {
    backgroundColor: '#fff',
    padding: 16,
    alignItems: 'center',
    borderBottomWidth: 1,
    borderBottomColor: '#e0e0e0',
  },
  statusText: {
    fontSize: 16,
    color: '#333',
    textAlign: 'center',
  },
  spinner: {
    marginTop: 8,
  },
  hostActions: {
    backgroundColor: '#fff',
    padding: 16,
    flexDirection: 'row',
    justifyContent: 'space-around',
    borderBottomWidth: 1,
    borderBottomColor: '#e0e0e0',
  },
  actionButton: {
    paddingHorizontal: 20,
    paddingVertical: 12,
    borderRadius: 8,
    minWidth: 120,
    alignItems: 'center',
  },
  primaryButton: {
    backgroundColor: '#2196F3',
  },
  secondaryButton: {
    backgroundColor: '#4CAF50',
  },
  actionButtonText: {
    color: '#fff',
    fontSize: 14,
    fontWeight: '600',
  },
  tabContainer: {
    backgroundColor: '#fff',
    flexDirection: 'row',
    borderBottomWidth: 1,
    borderBottomColor: '#e0e0e0',
  },
  tab: {
    flex: 1,
    paddingVertical: 12,
    alignItems: 'center',
    borderBottomWidth: 2,
    borderBottomColor: 'transparent',
  },
  activeTab: {
    borderBottomColor: '#2196F3',
  },
  tabText: {
    fontSize: 14,
    color: '#666',
  },
  activeTabText: {
    color: '#2196F3',
    fontWeight: '600',
  },
  contentContainer: {
    flex: 1,
  },
  // 聊天相关样式
  chatContainer: {
    flex: 1,
    backgroundColor: '#fff',
  },
  messagesList: {
    flex: 1,
    paddingHorizontal: 16,
  },
  messageItem: {
    marginVertical: 4,
    padding: 8,
    backgroundColor: '#f8f9fa',
    borderRadius: 8,
  },
  messageUser: {
    fontSize: 12,
    color: '#666',
    fontWeight: '600',
  },
  messageText: {
    fontSize: 14,
    color: '#333',
    marginTop: 2,
  },
  messageInputContainer: {
    flexDirection: 'row',
    padding: 16,
    backgroundColor: '#fff',
    borderTopWidth: 1,
    borderTopColor: '#e0e0e0',
  },
  messageInput: {
    flex: 1,
    borderWidth: 1,
    borderColor: '#ddd',
    borderRadius: 20,
    paddingHorizontal: 16,
    paddingVertical: 8,
    marginRight: 8,
    maxHeight: 80,
  },
  sendButton: {
    backgroundColor: '#2196F3',
    borderRadius: 20,
    paddingHorizontal: 16,
    paddingVertical: 8,
    justifyContent: 'center',
  },
  sendButtonText: {
    color: '#fff',
    fontSize: 14,
    fontWeight: '600',
  },
  // 历史记录样式
  historyContainer: {
    flex: 1,
    backgroundColor: '#fff',
    padding: 16,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#333',
    marginBottom: 16,
  },
  historyItem: {
    backgroundColor: '#f8f9fa',
    padding: 12,
    borderRadius: 8,
    marginBottom: 8,
  },
  historyTitle: {
    fontSize: 14,
    fontWeight: '600',
    color: '#333',
  },
  historyDescription: {
    fontSize: 12,
    color: '#666',
    marginTop: 4,
  },
  historyTime: {
    fontSize: 10,
    color: '#999',
    marginTop: 4,
  },
  // 参与者样式
  participantsContainer: {
    flex: 1,
    backgroundColor: '#fff',
    padding: 16,
  },
  participantItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#f0f0f0',
  },
  participantName: {
    fontSize: 14,
    color: '#333',
    fontWeight: '500',
  },
  participantStatus: {
    fontSize: 12,
  },
  emptyText: {
    textAlign: 'center',
    color: '#999',
    fontSize: 14,
    marginTop: 20,
  },
});
